from pathlib import Path

import cv2
from ultralytics import YOL<PERSON>


def save_detection_image(
    model_path: str,
    image_path: str,
    output_path: str = None,
    conf: float = 0.3,
    iou: float = 0.3,
    imgsz: int = 384,
    max_det: int = 4096,
    line_width: int = 2,
    show_labels: bool = True,
    show_conf: bool = True,
    show: bool = False,
):
    """
    Run YOLO inference and save the image with detection visualizations.

    Args:
        model_path: Path to YOLO model weights
        image_path: Path to input image
        output_path: Path to save output image (if None, auto-generate)
        conf: Confidence threshold
        iou: IoU threshold
        imgsz: Input image size
        max_det: Maximum detections
        line_width: Bounding box line width
        show_labels: Show class labels
        show_conf: Show confidence scores
        show: Display image window

    Returns:
        Path to saved image
    """
    # Load model
    model = YOLO(model_path)

    # Generate output path if not provided
    if output_path is None:
        input_path = Path(image_path)
        output_dir = input_path.parent / "detections"
        output_dir.mkdir(exist_ok=True)
        output_path = output_dir / f"{input_path.stem}_detections{input_path.suffix}"

    # Run inference with save option
    results = model.predict(
        image_path,
        conf=conf,
        iou=iou,
        show=show,
        save=True,  # This saves to runs/detect/predict
        line_width=line_width,
        imgsz=imgsz,
        max_det=max_det,
        show_labels=show_labels,
        show_conf=show_conf,
        project="output",  # Custom project directory
        name="detections",  # Custom run name
        exist_ok=True,  # Overwrite existing results
    )

    print("Number of results: ", len(results[0]))

    # Also save to custom location if specified
    if results and len(results) > 0:
        # Get the annotated image
        annotated_img = results[0].plot(line_width=line_width, labels=show_labels, conf=show_conf)

        # Convert RGB to BGR for OpenCV
        annotated_img_bgr = cv2.cvtColor(annotated_img, cv2.COLOR_RGB2BGR)

        # Save to custom location
        cv2.imwrite(str(output_path), annotated_img_bgr)
        print(f"Detection image saved to: {output_path}")

        # Print detection summary
        if results[0].boxes is not None:
            num_detections = len(results[0].boxes)
            print(f"Found {num_detections} detections")

            # Print confidence scores
            if num_detections > 0:
                confidences = results[0].boxes.conf.cpu().numpy()
                print(f"Confidence range: {confidences.min():.3f} - {confidences.max():.3f}")
        else:
            print("No detections found")

    return str(output_path)


if __name__ == "__main__":
    # Configuration
    model_path = r"C:\Users\<USER>\source\tobacco_det\runs\detect\train4\weights\best.pt"
    image_path = r"C:\Users\<USER>\source\tobacco_det_data\data_yolo\val\images\tod_P061303_15.tif"

    # Save detection image
    output_path = save_detection_image(
        model_path=model_path,
        image_path=image_path,
        conf=0.3,
        iou=0.3,
        imgsz=384,
        max_det=4096,
        line_width=1,
        show_labels=False,
        show_conf=True,
        show=True,  # Set to False if you don't want to display the window
    )

    print(f"Detection visualization saved to: {output_path}")
